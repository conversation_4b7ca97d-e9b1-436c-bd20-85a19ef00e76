import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { FormsModule } from '@angular/forms';
import { CBResponse, IdNameModel } from 'src/app/shared/models';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MatInputModule } from '@angular/material/input';
import { MatIconModule } from '@angular/material/icon';
import { CommonUtils } from 'src/app/shared/utils';
import { All } from '../plan/models/plan-summary.model';
import { GroupClassesService } from 'src/app/pages/schedule-classes/pages/group-class/services';
import {
  DateFilterTypeEnum,
  GroupClassFilters,
  GroupClassScheduleSummary
} from 'src/app/pages/schedule-classes/pages/group-class/models/group-class.model';
import { LessonTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { Instrument } from 'src/app/request-information/models';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { AddGroupClassComponent } from './pages/add-group-class/add-group-class.component';
import { UpdateGroupClassComponent } from './pages/update-group-class/update-group-class.component';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { ViewGroupClassComponent } from './pages/view-group-class/view-group-class.component';
import { NgxPaginationModule } from 'ngx-pagination';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { ActivatedRoute, Router } from '@angular/router';
import { MultiSelectComponent } from 'src/app/shared/components/multi-select/multi-select.component';
import { Debounce } from 'src/app/shared/decorators';
import { LocalDatePipe } from 'src/app/shared/pipe';
import { DateUtils } from 'src/app/shared/utils/date.utils';

const DEPENDENCIES = {
  MODULES: [
    SharedModule,
    MatSidenavModule,
    MatSelectModule,
    MatButtonModule,
    CommonModule,
    FormsModule,
    MatInputModule,
    MatIconModule,
    NgxPaginationModule
  ],
  COMPONENTS: [AddGroupClassComponent, ViewGroupClassComponent, UpdateGroupClassComponent, MultiSelectComponent],
  PIPES: [LocalDatePipe]
};

@Component({
  selector: 'app-group-class-creation',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.COMPONENTS, ...DEPENDENCIES.PIPES],
  templateUrl: './group-class-creation.component.html',
  styleUrl: './group-class-creation.component.scss'
})
export class GroupClassCreationComponent extends BaseComponent implements OnInit {
  isAddGroupClassSideNavOpen = false;
  isViewGroupClassOpen = false;
  isEditFromView = false;
  isCloneSideNavOpen = false;

  selectedGroupId!: number | null;

  groupClassDetails: Array<GroupClassScheduleSummary> = [];
  instruments!: Array<Instrument>;
  locations!: Array<SchoolLocations>;

  pageSize = this.paginationConfig.itemsPerPage;
  currentPage = this.paginationConfig.pageNumber;
  totalCount!: number;
  lessonType = LessonTypes;
  all = All;
  filters: GroupClassFilters = {
    instrumentIdFilter: {
      id: 1,
      defaultPlaceholder: 'All Instruments',
      placeholder: 'All Instruments',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    locationIdFilter: {
      id: 2,
      defaultPlaceholder: 'All Locations',
      placeholder: 'All Locations',
      value: new Set(),
      totalCount: 0,
      isOpen: false,
      showSearchBar: true,
      showClassBorder: false,
      options: [] as Array<IdNameModel>
    },
    ageGroupFilter: 0,
    currentDateFilter: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss)
  };

  pageTabOptions = { ONGOING: 'Ongoing Class', UPCOMING: 'Upcoming Class', PAST: 'Past Class' };
  selectedTabOption = this.pageTabOptions.ONGOING;

  constructor(
    private readonly groupClassService: GroupClassesService,
    private readonly commonService: CommonService,
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly cdr: ChangeDetectorRef,
    protected readonly schedulerService: SchedulerService,
    private readonly router: Router,
    private readonly activatedRoute: ActivatedRoute,
    private readonly datePipe: DatePipe
  ) {
    super();
  }

  ngOnInit(): void {
    this.setActiveTabFromQueryParams();
    this.getInstruments();
    this.getLocations();
  }

  toggleAddEditSideNav(isOpen: boolean, isView: boolean, groupClassId: number | null, isEditFromView = false): void {
    this.isAddGroupClassSideNavOpen = isOpen;
    this.isViewGroupClassOpen = isView;
    this.selectedGroupId = groupClassId;
    this.isEditFromView = isEditFromView;
  }

  setActiveTabFromQueryParams(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (Object.keys(params).length) {
        this.selectedTabOption = params.activeTab;
        this.getGroupClassDetail(this.currentPage, this.pageSize);
        return;
      }
      this.setActiveTabOption(this.pageTabOptions.ONGOING);
    });
  }

  setActiveTabOption(tabName: string): void {
    this.selectedTabOption = tabName;
    this.router.navigate([this.path.settings.root, this.path.settings.groupClass], {
      queryParams: {
        activeTab: tabName
      }
    });
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getGroupClassDetail(this.currentPage, this.pageSize);
  }

  getFilterParams(currentPage: number, pageSize: number) {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      InstrumentIdFilter: [...this.filters.instrumentIdFilter.value],
      LocationIdFilter: [...this.filters.locationIdFilter.value],
      AgeGroupFilter: this.filters.ageGroupFilter,
      scheduleStartDate: DateUtils.getUtcRangeForLocalDate(this.filters.currentDateFilter ?? '').startUtc,
      scheduleEndDate: DateUtils.getUtcRangeForLocalDate(this.filters.currentDateFilter ?? '').endUtc,
      DateFilterType: this.getDateTypeFilter(),
      Page: currentPage,
      PageSize: pageSize
    });
  }

  getDateTypeFilter(): number {
    switch (this.selectedTabOption) {
      case this.pageTabOptions.PAST:
        return DateFilterTypeEnum.PAST_CLASSES;
      case this.pageTabOptions.UPCOMING:
        return DateFilterTypeEnum.UPCOMING_CLASSES;
      default:
        return DateFilterTypeEnum.ONGOING_CLASS;
    }
  }

  @Debounce(500)
  getGroupClassDetail(currentPage: number, pageSize: number): void {
    this.showPageLoader = true;
    this.cdr.detectChanges();
    this.groupClassService
      .add(this.getFilterParams(currentPage, pageSize), `${API_URL.crud.getAll}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<GroupClassScheduleSummary>) => {
          this.groupClassDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instruments = res.result.items;
          this.filters.instrumentIdFilter.options = res.result.items.map(instrument => ({
            id: instrument.instrumentDetail.id,
            name: instrument.instrumentDetail.name
          }));
          this.filters.instrumentIdFilter.totalCount = this.instruments.length;
          this.cdr.detectChanges();
        }
      });
  }

  getLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.filters.locationIdFilter.options = res.result.items.map(location => ({
            id: location.schoolLocations.id,
            name: location.schoolLocations.locationName
          }));
          this.filters.locationIdFilter.totalCount = this.locations.length;
          this.cdr.detectChanges();
        }
      });
  }

  deleteGroupClassConfirmation(id: number): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Group Class`,
        message: `Are you sure you want to delete this group class?`
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result.isConfirmed) {
        this.deleteGroupClass(id);
      }
    });
  }

  deleteGroupClass(id: number): void {
    this.groupClassService
      .delete(id, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Group Class'));
          this.getGroupClassDetail((this.currentPage = 1), this.pageSize);
        }
      });
  }

  onCloseSideNav(): void {
    this.isAddGroupClassSideNavOpen = false;
  }

  keepOriginalOrder = () => 0;
}
