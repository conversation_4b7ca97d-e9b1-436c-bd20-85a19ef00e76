import { HttpClient } from '@angular/common/http';
import { Injectable, Injector } from '@angular/core';
import { catchError, map, Observable, of } from 'rxjs';
import { LoginResponse } from 'src/app/auth/models';
import { API_URL } from '../constants/api-url.constants';
import { LocalStorageService, StorageItem } from './local-storage.service';
import { AuthService } from 'src/app/auth/services';

@Injectable({ providedIn: 'root' })
export class RefreshTokenService {
  private refreshTokenTimeout!: NodeJS.Timeout;
   private authService!: AuthService;

  constructor(
    private readonly httpClient: HttpClient,
    private readonly localStorageService: LocalStorageService,
    private readonly injector: Injector,
  ) {}

  startRefreshTokenTimer(expireTime: number) {
    // parse json object from base64 encoded jwt token
    const jwtToken = expireTime;

    const expires = jwtToken * 500;
    console.log('[REFRESH_TOKEN] Starting refresh token timer. Expires in:', expires, 'ms (', expires/1000/60, 'minutes)');
    this.refreshTokenTimeout = setTimeout(() => {
      console.log('[REFRESH_TOKEN] Timer expired - attempting token refresh');
      this.refreshToken().subscribe();
    }, expires);
  }

  stopRefreshTokenTimer() {
    clearTimeout(this.refreshTokenTimeout);
  }

  private getAuthService(): AuthService {
    if (!this.authService) {
      this.authService = this.injector.get(AuthService);
    }
    return this.authService;
  }

  refreshToken(): Observable<LoginResponse | null> {
    const refreshToken = this.localStorageService.getItem(StorageItem.RefreshToken) as string;
    console.log('[REFRESH_TOKEN] Attempting to refresh token. RefreshToken exists:', !!refreshToken);

    if (!refreshToken) {
      console.log('[REFRESH_TOKEN] No refresh token found - triggering logout');
      this.stopRefreshTokenTimer();
      this.getAuthService().logOut();
      return of(null);
    }

    if (this.isTokenExpired(refreshToken)) {
      console.log('[REFRESH_TOKEN] Refresh token is expired - triggering logout');
      this.stopRefreshTokenTimer();
      this.getAuthService().logOut();
      return of(null);
    }

    console.log('[REFRESH_TOKEN] Refresh token is valid, proceeding with refresh');
    console.log('[REFRESH_TOKEN] Making API call to refresh token...');
    return this.httpClient
      .post<LoginResponse>(
        `${API_URL.account.tokeAuth}/${API_URL.account.refreshAccessToken}?refreshToken=${refreshToken}`,
        {}
      )
      .pipe(
        map((response) => {
          console.log('[REFRESH_TOKEN] Refresh token API call successful:', response);
          console.log('[REFRESH_TOKEN] Storing new access token');
          this.localStorageService.setItem(StorageItem.AuthToken, response.result.accessToken);
          this.startRefreshTokenTimer(response.result.expireInSeconds);
          return response;
        }),
        catchError((error) => {
          console.log('[REFRESH_TOKEN] Refresh token API call failed:', error);
          console.log('[REFRESH_TOKEN] Error status:', error.status);
          console.log('[REFRESH_TOKEN] Error message:', error.message);
          this.getAuthService().logOut();
          return of(null);
        })
      );
  }

  storeAuthTokens(loginResponse: LoginResponse) {
    this.localStorageService.setItem(StorageItem.AuthToken, loginResponse.result.accessToken);
    this.localStorageService.setItem(StorageItem.RefreshToken, loginResponse.result.refreshToken);
  }

  isTokenExpired(token: string): boolean {
    try {
      const payload = JSON.parse(atob(token.split('.')[1])); 
      const expiryTime = payload.exp * 1000;
      return expiryTime < Date.now();
    } catch (error) {
      return true;
    }
  }
}
