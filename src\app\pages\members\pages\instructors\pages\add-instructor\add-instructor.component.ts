import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output, SimpleChanges } from '@angular/core';
import {
  AbstractControl,
  AbstractControlOptions,
  FormArray,
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators
} from '@angular/forms';
import { MatButtonModule } from '@angular/material/button';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { takeUntil } from 'rxjs';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { CommonModule, DatePipe } from '@angular/common';
import { NgxMaskDirective, provideNgxMask } from 'ngx-mask';
import {
  AvailabilityType,
  GradeLevel,
  InstructorAvaibilityInInstructorDetail,
  InstructorAvailabilityFormGroup,
  InstructorDetails,
  InstructorInstrumentFormGroup,
  InstructorsFormGroup,
  LabelValueKey,
  LeaveBalanceFormGroup,
  MemberDayWiseLeaveFormGroup
} from '../../models';
import { RoomDetails, RoomInstrumentList, SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { InstructorService } from 'src/app/schedule-introductory-lesson/services';
import { Instrument } from 'src/app/request-information/models';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatCheckboxModule } from '@angular/material/checkbox';
import { RoomService } from 'src/app/pages/room-and-location-management/pages/room/services';
import { provideNativeDateAdapter } from '@angular/material/core';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { NgxMaterialTimepickerModule } from 'ngx-material-timepicker';
import { EnumToKeyValuePipe } from 'src/app/shared/pipe';
import moment from 'moment';
import {
  dateOfBirthValidator,
  outOfRangeTimeValidator,
  phoneNumberValidator,
  timeRangeValidator,
  usedLeaveDaysValidator,
  zipCodeValidator
} from 'src/app/shared/validators';
import { InstructorInstrument } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { State } from 'src/app/auth/models/user.model';
import { LeaveBalance, LeaveType, MemberDayViseLeave } from 'src/app/pages/requests/pages/leave-request/models';
import { LocationService } from 'src/app/pages/room-and-location-management/pages/location/services';
import { DateUtils } from 'src/app/shared/utils/date.utils';
import { CommonUtils } from 'src/app/shared/utils';

const DEPENDENCIES = {
  MODULES: [
    MatButtonModule,
    ReactiveFormsModule,
    MatInputModule,
    MatFormFieldModule,
    SharedModule,
    MatSelectModule,
    NgxMaskDirective,
    CommonModule,
    MatCheckboxModule,
    MatDatepickerModule,
    NgxMaterialTimepickerModule
  ],
  PIPES: [EnumToKeyValuePipe]
};
@Component({
  selector: 'app-add-instructor',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNgxMask(), provideNativeDateAdapter()],
  templateUrl: './add-instructor.component.html',
  styleUrl: './add-instructor.component.scss'
})
export class AddInstructorComponent extends BaseComponent implements OnInit {
  @Input() selectedInstructorDetails!: InstructorDetails | null;
  @Input() isSupervisor!: boolean;

  instructorFormGroup!: FormGroup<InstructorsFormGroup>;
  locations!: Array<SchoolLocations>;
  instrumentTypes!: Array<Instrument>;
  gradeLevels!: Array<GradeLevel>;
  states!: Array<State>;
  rooms!: Array<RoomDetails>;
  maxDate = new Date();
  availabilityTypes = AvailabilityType;
  leaveTypes = LeaveType;
  leaveData!: MemberDayViseLeave[];

  @Output() closeSideNav = new EventEmitter<InstructorDetails | null>();
  @Output() isInstructorAdded = new EventEmitter<void>();

  constructor(
    private readonly instructorService: InstructorService,
    private readonly roomService: RoomService,
    private readonly toasterService: AppToasterService,
    private readonly commonService: CommonService,
    private readonly locationService: LocationService,
    private readonly dialog: MatDialog,
    private readonly datePipe: DatePipe,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.initInstructorForm();
    this.getInstruments();
    if (!this.selectedInstructorDetails) {
      this.addNewInstruments();
      this.addNewLocationAndInstructorAvailability();
      this.addLeaveBalance(this.leaveObject(LeaveType.PAID));
      this.addLeaveBalance(this.leaveObject(LeaveType.UNPAID));
    }
    this.getGradeLevels();
    this.getAllStates();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedInstructorDetails']?.currentValue) {
      this.showPageLoader = true;
      this.selectedInstructorDetails = changes['selectedInstructorDetails'].currentValue;
    }
  }

  ngAfterViewInit(): void {
    this.setInstructorForm();
  }

  initInstructorForm(): void {
    this.instructorFormGroup = new FormGroup<InstructorsFormGroup>({
      id: new FormControl(undefined, { nonNullable: true }),
      name: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.NAME_PATTERN)]
      }),
      email: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, Validators.pattern(this.constants.pattern.EMAIL)]
      }),
      phoneNumber: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, phoneNumberValidator()]
      }),
      address: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      stateId: new FormControl(undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      city: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required]
      }),
      zipCode: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, zipCodeValidator()]
      }),
      dateOfBirth: new FormControl('', {
        nonNullable: true,
        validators: [Validators.required, dateOfBirthValidator()]
      }),
      isSupervisor: new FormControl(this.isSupervisor ?? false, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      showAvailability: new FormControl(this.selectedInstructorDetails?.instructorAvailability ? this.selectedInstructorDetails?.instructorAvailability.length! > 0 : true, {
        nonNullable: true
      }),
      roleId: new FormControl(this.isSupervisor ? this.constants.roleIds.SUPERVISOR : this.constants.roleIds.INSTRUCTOR, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      instructorInstrument: new FormArray([] as FormGroup<InstructorInstrumentFormGroup>[]),
      instructorAvailability: new FormArray([] as FormGroup<InstructorAvailabilityFormGroup>[]),
      leaveBalances: new FormArray([] as FormGroup<LeaveBalanceFormGroup>[])
    });
  }

  setInstructorForm(): void {
    if (this.selectedInstructorDetails) {
      this.instructorFormGroup?.patchValue({ ...this.selectedInstructorDetails });
      if(!this.selectedInstructorDetails.instructorAvailability?.length) {
        this.setFormControlValue('showAvailability', false, 0);
      }
      this.setInstruments(this.selectedInstructorDetails.instruments);
      this.setLocationAndAvailability(this.selectedInstructorDetails.instructorAvailability);
      this.setLeaveBalance(this.selectedInstructorDetails.leaveBalances);
    }
  }

  setInstruments(instruments: InstructorInstrument[] | undefined): void {
    const instrumentFormArray = this.getInstructorFormArray('instructorInstrument');
    instrumentFormArray.clear();
    instruments?.forEach((instrument, i) => {
      this.addNewInstruments(instrument);
      if (this.selectedInstructorDetails) {
        this.setGradeLevel(i);
        const lastIndex = instrumentFormArray.length - 1;
        instrumentFormArray.at(lastIndex).get('instrumentId')?.disable();
      }
    });
  }

  setLocationAndAvailability(instructorAvailability: InstructorAvaibilityInInstructorDetail[] | undefined): void {
    const availabilityFormArray = this.getInstructorFormArray('instructorAvailability');
    availabilityFormArray.clear();
    instructorAvailability?.forEach((availability, i) => {
      this.addNewLocationAndInstructorAvailability(availability);
      this.getRooms(i);
      this.getRoomsByLocation(i);
      availability.availableDays?.forEach(day => this.setDaysOfWeek(day, i));
      if (this.selectedInstructorDetails) {
        const lastIndex = availabilityFormArray.length - 1;
        availabilityFormArray.at(lastIndex).get('locationId')?.disable();
      }
    });
    this.showPageLoader = false;
  }

  setLeaveBalance(leaveBalances: LeaveBalance[] | undefined): void {
    const leaveBalanceFormArray = this.getInstructorFormArray('leaveBalances');
    leaveBalanceFormArray.clear();
    if (leaveBalances && leaveBalances.length) {
      leaveBalances[0].memberDayViseLeaves?.forEach(leave => {
        this.leaveData = this.leaveData || [];
        this.leaveData.push({
          day: leave.day,
          usedLeaveDays: leave.usedLeaveDays,
          remainingLeaveDays: leave.remainingLeaveDays,
          totalLeaveDays: leave.totalLeaveDays
        });
      });
    }
    leaveBalances?.forEach(leave => {
      this.addLeaveBalance(leave);
    });
    this.cdr.detectChanges();
  }

  getMinStartDate(index: number): Date {
    const availabilityControl = this.getInstructorFormArray('instructorAvailability').at(index);
    const availableStartDate = availabilityControl.get('availableStartDate')?.value;

    const startDate = availableStartDate ? new Date(availableStartDate) : this.maxDate;
    return startDate > this.maxDate ? this.maxDate : startDate;
  }

  resetValues(index: number): void {
    this.getInstructorFormArray('instructorAvailability').at(index)?.get('availabilityType')?.setValue(null);
    this.getInstructorFormArray('instructorAvailability').at(index)?.get('availableEndDate')?.setValue(null);
    this.getInstructorFormArray('instructorAvailability').at(index)?.get('availableStartTime')?.setValue(null);
    this.getInstructorFormArray('instructorAvailability').at(index)?.get('availableEndTime')?.setValue(null);
    this.getInstructorFormArray('instructorAvailability').at(index)?.get('neverEnd')?.setValue(null);
    (this.getInstructorFormArray('instructorAvailability').at(index)?.get('availableDays') as FormArray).clear();
  }

  getInstructorFormArray(controlName: string): FormArray {
    return this.instructorFormGroup?.get(controlName) as FormArray;
  }

  addNewInstruments(instrument?: InstructorInstrument): void {
    const formGroup = new FormGroup<Record<string, AbstractControl>>({
      id: new FormControl(0, { nonNullable: true }),
      instrumentId: new FormControl(instrument?.id ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      gradeLevel: new FormControl(instrument?.gradeLevel ?? undefined, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      isIntroductoryClassAvailable: new FormControl(instrument?.isIntroductoryClassAvailable ?? false, {
        nonNullable: true
      })
    });

    if (this.isSupervisor) {
      formGroup.addControl('isHeadOfDepartment', new FormControl(instrument?.isHeadOfDepartment ?? false, { nonNullable: true }));
    }

    this.getInstructorFormArray('instructorInstrument')?.push(formGroup);
  }

  getFormattedStartTime(i: number): string {
    return this.datePipe.transform(this.selectedInstructorDetails?.instructorAvailability[i]?.availableStartTime, 'shortTime') ?? '';
  }

  getFormattedEndTime(i: number): string {
    return this.datePipe.transform(this.selectedInstructorDetails?.instructorAvailability[i]?.availableEndTime, 'shortTime') ?? '';
  }

  addNewLocationAndInstructorAvailability(instructorAvailability?: InstructorAvaibilityInInstructorDetail): void {
    const endDate = this.datePipe.transform(instructorAvailability?.availableEndDate, this.constants.dateFormats.yyyy_MM_dd);

    this.getInstructorFormArray('instructorAvailability')?.push(
      new FormGroup(
        {
          id: new FormControl(instructorAvailability?.id ?? 0, { nonNullable: true }),
          availableStartTime: new FormControl(instructorAvailability?.availableStartTime ?? '', {
            nonNullable: true,
            validators: [outOfRangeTimeValidator()]
          }),
          availableEndTime: new FormControl(instructorAvailability?.availableEndTime ?? '', {
            nonNullable: true,
            validators: [outOfRangeTimeValidator()]
          }),
          availableStartDate: new FormControl(instructorAvailability?.availableStartDate ?? '', {
            nonNullable: true
          }),
          availableEndDate: new FormControl(instructorAvailability?.availableEndDate ?? '', { nonNullable: true }),
          availableDays: new FormArray([] as FormControl<number>[]),
          locationId: new FormControl(instructorAvailability?.locationId ?? undefined, {
            nonNullable: true
          }),
          availabilityType: new FormControl(instructorAvailability?.availabilityType ?? undefined, {
            nonNullable: true
          }),
          roomId: new FormControl(instructorAvailability?.roomId ?? undefined, {
            nonNullable: true
          }),
          roomScheduleSummaryId: new FormControl(instructorAvailability?.roomScheduleSummaryId ?? undefined, {
            nonNullable: true
          }),
          neverEnd: new FormControl(endDate === this.getFiveYearLaterDate(instructorAvailability?.availableStartDate), {
            nonNullable: true
          })
        },
        { validators: timeRangeValidator('availableStartTime', 'availableEndTime') } as AbstractControlOptions
      )
    );
    this.getAllLocations();
  }

  addLeaveBalance(leave?: LeaveBalance): void {
    const formGroup = new FormGroup<Record<string, AbstractControl>>({
      leaveType: new FormControl(leave?.leaveType ?? 1, {
        nonNullable: true,
        validators: [Validators.required]
      }),
      totalLeaveDays: new FormControl(leave?.totalLeaveDays ?? 0, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(0)]
      }),
      usedLeaveDays: new FormControl(leave?.usedLeaveDays ?? 0, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(0), usedLeaveDaysValidator()]
      }),
      remainingLeaveDays: new FormControl(leave?.remainingLeaveDays ?? 0, {
        nonNullable: true,
        validators: [Validators.required, Validators.min(0)]
      }),
      memberDayViseLeaves: new FormArray([] as FormGroup<MemberDayWiseLeaveFormGroup>[])
    });

    if (leave?.leaveType === this.leaveTypes.UNPAID) {
      formGroup.get('totalLeaveDays')?.disable();
    }

    this.getInstructorFormArray('leaveBalances')?.push(formGroup);
  }

  getRemainingLeaveDays(index: number): number {
    const group = this.getInstructorFormArray('leaveBalances').at(index) as FormGroup;
    const total = group.get('totalLeaveDays')?.value || 0;
    const used = group.get('usedLeaveDays')?.value || 0;
    const remaining = Math.max(total - used, 0);

    // Update the remainingLeaveDays control
    group.get('remainingLeaveDays')?.setValue(remaining);

    return remaining;
  }

  toggleAvailability(): void {
    const i = this.getInstructorFormArray('instructorAvailability').length - 1;
    if (this.instructorFormGroup.get('showAvailability')?.value) {
      this.setRequiredBasedOnCondition('availableStartTime', true, i);
      this.setRequiredBasedOnCondition('availableEndTime', true, i);
      this.setRequiredBasedOnCondition('availableStartDate', true, i);
      this.setRequiredBasedOnCondition('availableEndDate', true, i);
      this.setRequiredBasedOnCondition('locationId', true, i);
      this.setRequiredBasedOnCondition('availabilityType', true, i);
      this.setRequiredBasedOnCondition('roomId', true, i);
      this.instructorFormGroup.updateValueAndValidity();
      this.cdr.detectChanges();
    }
    else {
      this.setRequiredBasedOnCondition('availableStartTime', false, i);
      this.setRequiredBasedOnCondition('availableEndTime', false, i);
      this.setRequiredBasedOnCondition('availableStartDate', false, i);
      this.setRequiredBasedOnCondition('availableEndDate', false, i);
      this.setRequiredBasedOnCondition('locationId', false, i);
      this.setRequiredBasedOnCondition('availabilityType', false, i);
      this.setRequiredBasedOnCondition('roomId', false, i);
      this.cdr.detectChanges();
    }
  }

  getAllLocations(i?: number): void {
    i ? this.getInstructorFormArray('instructorAvailability').controls[i]?.get('locationId')?.reset() : '';
    const instrumentIds = this.getInstructorFormArray('instructorInstrument')
      .controls.map(control => control.get('instrumentId')?.value)
      .filter((id): id is number => id !== undefined && id !== null);
    this.locationService
      .add({ instrumentIds: instrumentIds }, API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getInstruments(): void {
    this.commonService
      .getInstruments()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<Instrument>) => {
          this.instrumentTypes = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getAllStates(): void {
    this.commonService
      .getStates()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<Array<State>>) => {
          this.states = res.result;
          this.cdr.detectChanges();
        }
      });
  }

  getRooms(i: number): void {
    this.getInstructorFormArray('instructorAvailability').controls[i]?.get('roomId')?.reset();
    const instrumentIds = this.getInstructorFormArray('instructorInstrument').controls.map(control => control.get('instrumentId')?.value).filter((id): id is number => id !== undefined && id !== null);
    this.roomService
      .add({ page: 1, instrumentIdFilter: instrumentIds }, API_URL.crud.getAll)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<RoomDetails>) => {
          this.rooms = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getRoomsByLocation(i: number): RoomDetails[] {
    return this.rooms?.filter(
      room => room.roomDetail.locationId === this.getInstructorFormArray('instructorAvailability').controls[i]?.get('locationId')?.value
    );
  }

  getRoomInstrumentNames(i: number): RoomInstrumentList[] | undefined {
    const selectedRoomId = this.getInstructorFormArray('instructorAvailability').controls[i]?.get('roomId')?.value;
    return this.rooms?.find(room => room.roomDetail.id === selectedRoomId)?.roomDetail.roomInstrumentList;
  }

  getGradeLevels(): void {
    this.gradeLevels = [];
    for (let i = 0; i <= 10; i++) {
      this.gradeLevels.push({ gradeLevel: { id: i, name: String(i) } });
    }
  }

  setGradeLevel(i: number): void {
    const instrumentIds = this.instrumentTypes
      ?.filter(instrument => instrument.instrumentDetail.name === 'General Music' || instrument.instrumentDetail.name === 'Early Starters')
      .map(instrument => instrument.instrumentDetail.id);
    const selectedInstrumentId = this.getInstructorFormArray('instructorInstrument').controls[i]?.get('instrumentId')?.value;

    if (instrumentIds?.includes(selectedInstrumentId)) {
      this.getInstructorFormArray('instructorInstrument').controls[i]?.get('gradeLevel')?.disable();
      this.getInstructorFormArray('instructorInstrument').controls[i]?.get('gradeLevel')?.setValue(0);
    }
  }

  setFormControlValue(controlName: string, value: number | string | boolean | Date, i: number): void {
    const instructorForm = this.getInstructorFormArray('instructorAvailability').controls[i];
    instructorForm?.get(controlName)?.setValue(value);
    if (controlName === 'availabilityType') {
      this.setAvailabilityType(value, i);
    }
    if (controlName === 'availableEndDate') {
      instructorForm?.get('neverEnd')?.setValue(value === this.getFiveYearLaterDate(instructorForm?.get('availableStartDate')?.value));
    }
  }

  setAvailabilityType(value: number | string | boolean | Date, i: number): void {
    const instructorAvailability = this.getInstructorFormArray('instructorAvailability').controls[i];
    if (value === this.availabilityTypes.DAILY || value === this.availabilityTypes.WEEKLY) {
      this.setRequiredBasedOnCondition('availableEndDate', true, i);
    }
    if (value === this.availabilityTypes.WEEKLY) {
      this.setRequiredBasedOnCondition('availableDays', true, i);
    }
    if (value === this.availabilityTypes.DAILY) {
      this.setRequiredBasedOnCondition('availableDays', false, i);
      const availableDaysArray = instructorAvailability.get('availableDays') as FormArray;
      availableDaysArray.clear();
      this.constants.daysOfTheWeek.forEach(day => availableDaysArray.push(new FormControl(day.value, { nonNullable: true })));
    }
    if (value === this.availabilityTypes.NO_REPEATS) {
      this.setRequiredBasedOnCondition('availableDays', false, i);
      instructorAvailability.get('availableEndDate')?.setValue(instructorAvailability.get('availableStartDate')?.value);
      const availableDaysArray = instructorAvailability.get('availableDays') as FormArray;
      availableDaysArray.clear();
      availableDaysArray.push(
        new FormControl(moment(instructorAvailability.get('availableStartDate')?.value).day(), { nonNullable: true })
      );
    }
  }

  getDaysOfWeek(value: Array<number>): string {
    return value.map(day => this.constants.daysOfTheWeek.find(d => d.value === day)?.label).join(', ');
  }

  getFiveYearLaterDate(startDate?: string): string {
    return moment(startDate).add(5, 'year').format(this.constants.dateFormats.yyyy_MM_DD);
  }

  setDaysOfWeek(dayValue: number, i: number): void {
    const daysOfSchedule = this.getInstructorFormArray('instructorAvailability').controls[i].get('availableDays') as FormArray;
    const index = daysOfSchedule.value.indexOf(dayValue);

    if (index !== -1) {
      daysOfSchedule.removeAt(index);
    } else {
      daysOfSchedule.push(new FormControl(dayValue, { nonNullable: true }));
    }
  }

  isDaySelected(dayValue: number, i: number): boolean | undefined {
    const daysOfSchedule = this.getInstructorFormArray('instructorAvailability').controls[i].get('availableDays')?.value;
    return daysOfSchedule?.includes(dayValue);
  }

  setTime(control: string, i: number): void {
    const instructorAvailability = this.getInstructorFormArray('instructorAvailability').controls[i]?.get(control);
    const currentDate = this.datePipe.transform(
      this.getInstructorFormArray('instructorAvailability').controls[i]?.get('availableStartDate')?.value,
      this.constants.dateFormats.yyyy_MM_dd
    );

    const timeValue = this.datePipe.transform(
      new Date(`${currentDate} ${instructorAvailability?.value}`),
      this.constants.dateFormats.yyyy_MM_dd_T_HH_mm_ss
    );
    instructorAvailability?.setValue(timeValue ?? '');
  }

  setDate(control: string, i: number): void {
    this.setFormControlValue(
      control,
      this.datePipe.transform(
        this.getInstructorFormArray('instructorAvailability').controls[i]?.get(control)?.value,
        this.constants.dateFormats.yyyy_MM_dd
      )!,
      i
    );
  }

  setRequiredBasedOnCondition(controlName: string, required: boolean, i: number): void {
    const control = this.getInstructorFormArray('instructorAvailability').controls[i]?.get(controlName);
    if (required) {
      control?.setValidators([Validators.required]);
    } else {
      control?.clearValidators();
    }
    control?.updateValueAndValidity();
  }

  confirmationPopup(index: number, isInstrument: boolean): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete ${isInstrument ? 'Instrument' : 'Location & Availability'}`,
        message: `Are you sure you want to delete this ${isInstrument ? 'Instrument' : 'Location & Availability'}?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        isInstrument
          ? this.getInstructorFormArray('instructorInstrument').removeAt(index)
          : this.getInstructorFormArray('instructorAvailability').removeAt(index);
        this.cdr.detectChanges();
      }
    });
  }

  capUsedLeave(leaveData: any, event?: Event): void {
    if (leaveData.usedLeaveDays > leaveData.totalLeaveDays) {
      leaveData.usedLeaveDays = leaveData.totalLeaveDays;
      if (event) {
        (event.target as HTMLInputElement).value = leaveData.totalLeaveDays.toString();
      }
    }
  }

  get getLeaveBalanceDto(): LeaveBalance[] {
    if (this.showLeaveBalance()) {
      return [
        {
          leaveType: LeaveType.PAID,
          totalLeaveDays: this.leaveData.reduce((acc, item) => acc + item.totalLeaveDays, 0),
          usedLeaveDays: this.leaveData.reduce((acc, item) => acc + item.usedLeaveDays, 0),
          remainingLeaveDays: this.leaveData.reduce((acc, item) => acc + item.remainingLeaveDays, 0),
          memberDayViseLeaves: this.leaveData.map(item => ({
            day: item.day,
            usedLeaveDays: item.usedLeaveDays,
            remainingLeaveDays: item.remainingLeaveDays,
            totalLeaveDays: item.totalLeaveDays
          }))
        }
      ];
    }
    return [
      {
        leaveType: LeaveType.PAID,
        totalLeaveDays: 0,
        usedLeaveDays: 0,
        remainingLeaveDays: 0,
        memberDayViseLeaves: []
      }
    ];
  }

  setInstructorAvailability(): void {
    if (!this.instructorFormGroup.getRawValue().showAvailability) {
      this.getInstructorFormArray('instructorAvailability').clear();
      this.instructorFormGroup.get('instructorAvailability')?.setValue([]);
    }
  }

  onSubmit(): void {
    this.setInstructorAvailability();
    if (this.instructorFormGroup.invalid) {
      this.instructorFormGroup.markAllAsTouched();
      return;
    }
    this.instructorFormGroup.markAsUntouched();
    this.showBtnLoader = true;
    this.instructorService
      .add(
        {
          ...this.instructorFormGroup.getRawValue(),
          dateOfBirth: this.datePipe.transform(this.instructorFormGroup.getRawValue().dateOfBirth, this.constants.dateFormats.yyyy_MM_dd),
          leaveBalances: this.getLeaveBalanceDto,
          instructorAvailability: this.instructorFormGroup.getRawValue().showAvailability ? this.instructorFormGroup.getRawValue().instructorAvailability.map(item => ({
            ...item,
            availableStartTime: DateUtils.toUTC(item.availableStartTime, 'yyyy-MM-DDTHH:mm:ss'),
            availableEndTime: DateUtils.toUTC(item.availableEndTime, 'yyyy-MM-DDTHH:mm:ss'),
            availableStartDate: DateUtils.getUtcRangeForLocalDate(item.availableStartDate).startUtc,
            availableEndDate: DateUtils.getUtcRangeForLocalDate(item.availableEndDate).endUtc
          })) : [],
        },
        API_URL.crud.createOrEdit
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.showBtnLoader = false;
          this.selectedInstructorDetails
            ? this.toasterService.success(
                this.constants.successMessages.updatedSuccessfully.replace('{item}', `${this.isSupervisor ? 'Supervisor' : 'Instructor'}`)
              )
            : this.toasterService.success(
                this.constants.successMessages.addedSuccessfully.replace('{item}', `${this.isSupervisor ? 'Supervisor' : 'Instructor'}`)
              );
          this.isInstructorAdded.emit();
          this.closeSideNavFun();
          this.cdr.detectChanges();
        },
        error: () => {
          this.showBtnLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  leaveObject(leaveType: number): LeaveBalance {
    return {
      leaveType: leaveType,
      remainingLeaveDays: 0,
      totalLeaveDays: 0,
      usedLeaveDays: 0,
      memberDayViseLeaves: [
        {
          day: 0,
          usedLeaveDays: 0,
          remainingLeaveDays: 0,
          totalLeaveDays: 0,
          usedUnpaidLeaveDays: 0
        }
      ]
    };
  }

  showLeaveBalance(): boolean {
    return this.getInstructorFormArray('instructorAvailability').controls.some(
      ctrl =>
        ctrl.get('availabilityType')?.value === this.availabilityTypes.DAILY ||
        (ctrl.get('availabilityType')?.value === this.availabilityTypes.WEEKLY && ctrl.get('availableDays')?.value.length > 0)
    );
  }

  getLeaveData(day: number): any {
    return this.leaveData.find(item => item.day === day);
  }

  setLeaveData(leaveData: MemberDayViseLeave, controlName: any, event: Event): void {
    const day: any = this.leaveData.find(item => item.day === leaveData.day);
    day[controlName] = parseFloat((event.target as HTMLInputElement).value) || 0;
    day.remainingLeaveDays = Math.max(day.totalLeaveDays - day.usedLeaveDays, 0);
  }

  getDaysForTimeOff(): Array<LabelValueKey> {
    const availabilities = this.getInstructorFormArray('instructorAvailability').controls;
    const hasDaily = availabilities.some(ctrl => ctrl.get('availabilityType')?.value === this.availabilityTypes.DAILY);
    if (hasDaily) {
      this.ensureLeaveDataForDays(this.constants.daysOfTheWeek);
      return this.constants.daysOfTheWeek;
    }

    const allSelectedDays = new Set<number>();
    availabilities.forEach(ctrl => {
      const days: number[] = ctrl.get('availableDays')?.value || [];
      days.forEach(day => allSelectedDays.add(day));
    });
    this.ensureLeaveDataForDays(Array.from(allSelectedDays).map(day => ({ value: day })));
    return this.constants.daysOfTheWeek.filter(day => allSelectedDays.has(day.value));
  }

  ensureLeaveDataForDays(days: Array<{ value: number }>): void {
    if (!Array.isArray(this.leaveData)) {
      this.leaveData = [];
    }
    days.forEach(dayObj => {
      const exists = this.leaveData.some(ld => ld.day === dayObj.value);
      if (!exists) {
        this.leaveData.push({
          day: dayObj.value,
          usedLeaveDays: 0,
          remainingLeaveDays: 0,
          totalLeaveDays: 0
        });
      }
    });
    this.leaveData = this.leaveData.filter(ld => days.some(dayObj => dayObj.value === ld.day));
  }

  closeSideNavFun(): void {
    this.getInstructorFormArray('instructorInstrument').clear();
    this.getInstructorFormArray('instructorAvailability').clear();
    this.getInstructorFormArray('leaveBalances').clear();
    this.addNewInstruments();
    this.addNewLocationAndInstructorAvailability();
    this.addLeaveBalance(this.leaveObject(LeaveType.PAID));
    this.addLeaveBalance(this.leaveObject(LeaveType.UNPAID));
    this.closeSideNav.emit();
    this.instructorFormGroup.reset();
  }

  asIsOrder() {
    return 1;
  }
}
