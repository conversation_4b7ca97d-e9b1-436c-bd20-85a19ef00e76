@import 'src/assets/scss/theme/_mixins.scss';
@import 'src/assets/scss/variables';

.o-sidebar-wrapper {
  overflow: hidden;
  .back-btn-wrapper {
    @include flex-content-align-start;

    .name {
      @include flex-content-align-center;
    }
  }
}

.o-sidebar-body {
  padding: 10px 20px 30px 20px !important;
  height: calc(100vh - 85px);

  ::ng-deep .tab-item-content {
    .item {
      margin-right: 10px !important;
      padding: 4px 12px !important;
    }

    .active-item {
      background-color: $primary-color;
      border-radius: 10px;
      color: $white-color !important;
    }
  }

  .auth-page-with-header {
    overflow: hidden !important;
    height: calc(100vh - 160px) !important;
    padding: 0px 20px !important;

    .document-list {
      overflow: auto;
      height: calc(100vh - 225px);
      padding-right: 10px;
  
      .o-card {
        padding: 14px 20px;
        background-color: $gray-bg-light;
  
        .o-card-body {
          @include flex-content-space-between;
  
          .title {
            margin-bottom: 7px;
            font-size: 16px;
            font-weight: 700;
            color: $black-shade-text;
          }
  
          .attendance-img {
            margin-left: 8px;
            height: 18px !important;
            width: 18px !important;
  
            &.no-show {
              filter: $red-filter !important;
            }
  
            &.incomplete {
              filter: $yellow-filter !important;
            }
          }
  
          .student-content {
            @include flex-content-align-center;
            font-size: 14px;
            font-weight: 600;
          }
        }
      }
    }
  }

}

.no-data-found-card {
    height: calc(100vh - 190px) !important;
    background-color: $gray-bg-light;
}

::ng-deep {
  .mat-drawer-inner-container {
    overflow: hidden !important;
  }
  .sidenav-content-without-footer {
    overflow: hidden !important;
  }
}

@media (max-width: 767px) {
  .o-sidebar-body {
    .document-list {
      height: calc(100vh - 328px) !important;

      .o-card {
        .o-card-body,
        .document-content {
          flex-direction: column;
          align-items: flex-start !important;

          .dot {
            display: none;
          }
        }
      }
    }
  }
}
