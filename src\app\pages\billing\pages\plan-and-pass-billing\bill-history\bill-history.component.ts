import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { DirectivesModule } from 'src/app/shared/directives/directives.module';
import { CBGetResponse, CBResponse, IdNameModel } from 'src/app/shared/models';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { NgxPaginationModule } from 'ngx-pagination';
import { CommonUtils } from 'src/app/shared/utils';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { PlanSummaryService } from 'src/app/pages/settings/pages/plan/services';
import { PaymentService } from 'src/app/pages/schedule-classes/services';
import { PlanAndPassBillingFilters, BillStatus, BillHistoryRes } from '../../../models';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule } from '@angular/forms';
import { All } from 'src/app/pages/settings/pages/plan/models';
import { AuthService } from 'src/app/auth/services';
import { ActivatedRoute, Router } from '@angular/router';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { provideNativeDateAdapter } from '@angular/material/core';
import moment from 'moment';
import { MatIconModule } from '@angular/material/icon';
import { FilterPipe, LocalDatePipe } from 'src/app/shared/pipe';
import { DependentService } from 'src/app/pages/profile/services';
import { DependentInformations } from 'src/app/pages/members/pages/students/models';

const DEPENDENCIES = {
  MODULES: [
    CommonModule,
    DirectivesModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    NgxPaginationModule,
    SharedModule,
    MatSelectModule,
    FormsModule,
    MatTooltipModule,
    MatDatepickerModule,
    MatIconModule
  ],
  PIPES: [LocalDatePipe, FilterPipe]
};

@Component({
  selector: 'app-bill-history',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  providers: [provideNativeDateAdapter()],
  templateUrl: './bill-history.component.html',
  styleUrl: './bill-history.component.scss'
})
export class BillHistoryComponent extends BaseComponent implements OnInit {
  selectedDependentId!: number | undefined;
  selectedUserId!: number | undefined;
  billHistoryDetails!: Array<BillHistoryRes>;
  studentList!: Array<IdNameModel>;
  totalCount!: number;
  searchTerm = '';
  classType = ClassTypes;
  billStatus = BillStatus;
  all = All;

  filters: PlanAndPassBillingFilters = {
    statusFilter: 0,
    startDateFilter: moment().subtract(1, 'month').format(this.constants.dateFormats.yyyy_MM_DD),
    endDateFilter: moment().format(this.constants.dateFormats.yyyy_MM_DD)
  };

  constructor(
    private readonly cdr: ChangeDetectorRef,
    protected readonly planSummaryService: PlanSummaryService,
    protected readonly schedulerService: SchedulerService,
    private readonly paymentService: PaymentService,
    private readonly activatedRoute: ActivatedRoute,
    private readonly authService: AuthService,
    private readonly dependentService: DependentService,
    private readonly router: Router
  ) {
    super();
  }

  ngOnInit(): void {
    this.getCurrentUser();
  }

  getCurrentUser(): void {
    this.showPageLoader = true;
    this.authService
      .getCurrentUser()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: res => {
          this.currentUser = res;
          this.getCurrentId();
          this.getStudents();
          this.cdr.detectChanges();
        }
      });
  }

  getCurrentId(): void {
    this.activatedRoute.queryParams.subscribe((params: any) => {
      if (params.activeTab === 'Bill History') {
        if (params.dependentId) {
          this.selectedDependentId = +params.dependentId;
          this.selectedUserId = +params.userId;
        } else {
          this.selectedDependentId = 0;
          this.selectedUserId = 0;
        }
        this.getBillHistoryDetails();
      }
    });
  }

  openStudentDetailPage(dependentId: number): void {
    if (this.currentUser?.userRoleId === this.constants.roleIds.CLIENT) {
      return;
    }
    this.router.navigate([this.path.members.root, this.path.members.clients], {
      queryParams: { dependentId: dependentId }
    });
  }

  getFilterParamsForStudent(): void {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      InstructorFilter: this.currentUser?.userRoleId === this.constants.roleIds.INSTRUCTOR ? [this.currentUser?.dependentId] : [],
      locationFilter: []
    });
  }

  getStudents(): void {
    this.dependentService
      .add(this.getFilterParamsForStudent(), API_URL.dependentInformations.getAllStudents)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<DependentInformations[]>) => {
          this.studentList = res.result.map(item => ({
            id: item.id,
            accountManagerId: item.accountManagerId,
            name: `${item.firstName} ${item.lastName}`,
            age: item.age
          }));
          this.cdr.detectChanges();
        }
      });
  }

  setStudentDetail(student: IdNameModel | null): void {
    this.selectedDependentId = student?.id ?? 0;
    this.selectedUserId = student?.accountManagerId ?? 0;
    console.log(student);
    this.getBillHistoryDetails();
  }

  getFilterParams() {
    return CommonUtils.cleanObjectByRemovingKeysWithoutValue({
      UserId: this.currentUser?.userRoleId === this.constants.roleIds.CLIENT ? this.currentUser?.userId : this.selectedUserId,
      dependentInformationId: this.selectedDependentId,
      CreatedStartDate: moment(this.filters.startDateFilter).format(this.constants.dateFormats.yyyy_MM_DD),
      CreatedEndDate: moment(this.filters.endDateFilter).format(this.constants.dateFormats.yyyy_MM_DD)
    });
  }

  getBillHistoryDetails(): void {
    if (!this.filters.endDateFilter) {
      return;
    }
    this.showPageLoader = true;
    console.log(this.getFilterParams());
    this.paymentService
      .getListWithFilters<CBResponse<BillHistoryRes>>(this.getFilterParams(), `${API_URL.payment.getAllTransactionsOfUser}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<BillHistoryRes>) => {
          this.billHistoryDetails = res.result.items;
          this.totalCount = res.result.totalCount;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }
}
