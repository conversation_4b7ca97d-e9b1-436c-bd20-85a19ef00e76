import { ChangeDetectorRef, Component, EventEmitter, Input, OnChanges, OnInit, Output, SimpleChanges } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule, DatePipe } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { CommonUtils } from 'src/app/shared/utils';
import { MatIconModule } from '@angular/material/icon';
import { SchoolLocations } from 'src/app/pages/room-and-location-management/models';
import { takeUntil } from 'rxjs';
import { CBGetResponse, CBResponse, MatDialogRes } from 'src/app/shared/models';
import { DashIfEmptyPipe, LocalDatePipe } from 'src/app/shared/pipe';
import { AdvancedFilters, ClassTypes, ScheduleDetailsView } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { InstructorInformationParams, AvailabilityType } from '../../../instructors/models';
import { DeskManagerDetails } from '../../models';
import { EChartsOption } from 'echarts';
import { LeaveBalance } from 'src/app/pages/requests/pages/leave-request/models';
import { LeaveRequestService } from 'src/app/pages/requests/pages/leave-request/services';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { IdEmailModel } from '../../../students/models';
import { ChatService } from 'src/app/pages/messages/services';
import { ChatHistoryRes, ChatMessageType, FileType } from 'src/app/pages/messages/models';
import { MatTooltipModule } from '@angular/material/tooltip';
import { MatSidenavModule } from '@angular/material/sidenav';
import { MessagesComponent } from '../../../../../messages/messages.component';
import { AppToasterService, CommonService } from 'src/app/shared/services';
import { MatDialog } from '@angular/material/dialog';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { DeskManagerDetailsService } from '../../services';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, MatIconModule, MatIconModule, MatTooltipModule, MatSidenavModule],
  COMPONENTS: [MessagesComponent],
  PIPES: [DashIfEmptyPipe, LocalDatePipe]
};

@Component({
  selector: 'app-view-desk-manager',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES, ...DEPENDENCIES.COMPONENTS],
  templateUrl: './view-desk-manager.component.html',
  styleUrl: '../../../instructors/pages/view-instructor/view-instructor.component.scss'
})
export class ViewDeskManagerComponent extends BaseComponent implements OnInit, OnChanges {
  @Input() selectedDeskManagerViewDetails!: DeskManagerDetails | null;
  @Input() isSupervisor!: boolean;

  showScheduleLoader = false;
  isBioCollapsed = true;
  isLocationCollapsed = true;
  isMessageSideNavOpen = false;
  noIntroductoryInstrumentAvailable?: boolean;
  classTypes = ClassTypes;
  chatTypes = ChatMessageType;
  fileTypes = FileType;
  locations!: Array<SchoolLocations>;
  filters: InstructorInformationParams = {
    startDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? '',
    endDate: this.datePipe.transform(new Date(), this.constants.dateFormats.yyyy_MM_dd) ?? ''
  };
  appliedAdvanceFilter = new AdvancedFilters();
  events!: Array<ScheduleDetailsView>;
  chartOptions!: EChartsOption;
  totalLeaves!: number;
  usedLeaves!: number;
  availableLeaves!: number;
  leaveBalance!: LeaveBalance[];
  selectedIdEmail!: IdEmailModel | null;
  chatHistory!: Array<ChatHistoryRes> | null;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.twoItemsPerPage;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<void>();
  @Output() refreshDeskManagers = new EventEmitter<void>();

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly commonService: CommonService,
    private readonly datePipe: DatePipe,
    private readonly leaveRequestService: LeaveRequestService,
    private readonly deskManagerService: DeskManagerDetailsService,
    private readonly chatService: ChatService,
    private readonly toasterService: AppToasterService,
    private readonly dialog: MatDialog
  ) {
    super();
  }

  ngOnInit(): void {
    this.getAllLocations();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes['selectedDeskManagerViewDetails']?.currentValue) {
      this.selectedDeskManagerViewDetails = changes['selectedDeskManagerViewDetails'].currentValue;
      this.loadMessages();
      this.getLeaveBalance(this.selectedDeskManagerViewDetails?.email ?? '');
    }
  }

  getAllLocations(): void {
    this.commonService
      .getLocations()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<SchoolLocations>) => {
          this.locations = res.result.items;
          this.cdr.detectChanges();
        }
      });
  }

  getLeaveBalance(email: string): void {
    this.leaveRequestService
      .getList<CBGetResponse<LeaveBalance[]>>(`${API_URL.leaveManagement.getLeaveBalance}?UserEmail=${email}`)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<LeaveBalance[]>) => {
          this.leaveBalance = res.result;
          this.totalLeaves = this.leaveBalance[0].totalLeaveDays;
          this.usedLeaves = this.leaveBalance[0].usedLeaveDays;
          this.availableLeaves = this.leaveBalance[0].remainingLeaveDays;
          this.chartOptions = CommonUtils.getChartOptions(this.availableLeaves, this.usedLeaves, this.totalLeaves);
          this.cdr.detectChanges();
        }
      });
  }

  loadMessages(): void {
    this.showPageLoader = true;
    this.chatService
      .add(
        {
          page: this.currentPage,
          pageSize: this.pageSize,
          userEmail: this.selectedDeskManagerViewDetails?.email
        },
        API_URL.octopusChatAppServices.chatHistory
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBResponse<ChatHistoryRes>) => {
          this.chatHistory = res.result.items;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  toggleMessageSideNav(isOpen: boolean): void {
    this.isMessageSideNavOpen = isOpen;
    this.selectedIdEmail = isOpen ? { email: this.selectedDeskManagerViewDetails?.email } : null;
  }

  getTimeDiff(start: string, end: string): number | null {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  getAvailableDays(availableDays: number[]): string {
    const daysOfWeek = this.constants.daysOfTheWeek;
    return availableDays.map(day => daysOfWeek[day].label).join(', ');
  }

  onEdit(): void {
    this.openEditSideNav.emit();
  }

  onDeleteConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Staff Member`,
        message: `Are you sure you want to delete this staff member?`
      }
    });

    dialogRef.afterClosed().subscribe((result: MatDialogRes) => {
      if (result.isConfirmed) {
        this.onDeleteStaff(this.selectedDeskManagerViewDetails?.id);
      }
    });
  }

  onDeleteStaff(instructorId: number | undefined): void {
    if (!instructorId) return;
    this.showBtnLoader = true;
    this.deskManagerService.delete(instructorId, API_URL.crud.delete).subscribe({
      next: () => {
        this.showBtnLoader = false;
        this.refreshDeskManagers.emit();
        this.closeViewSideNavFun();
        this.toasterService.success(this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Staff Member'));
        this.cdr.detectChanges();
      },
      error: () => {
        this.showBtnLoader = false;
        this.cdr.detectChanges();
      }
    });
  }

  getInitials(name?: string): string {
    return CommonUtils.getInitialsUsingFullName(name);
  }

  getAvailabilityTypeName(value: number): string {
    return AvailabilityType[value];
  }

  toggleLocationCollapse() {
    this.isLocationCollapsed = !this.isLocationCollapsed;
  }

  closeViewSideNavFun(): void {
    this.isLocationCollapsed = true;
    this.closeViewSideNav.emit();
  }
}
