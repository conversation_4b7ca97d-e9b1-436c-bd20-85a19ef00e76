import { ChangeDetectorRef, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { SharedModule } from 'src/app/shared/shared.module';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { CommonModule } from '@angular/common';
import { MatButtonModule } from '@angular/material/button';
import { ConfirmationDialogComponent } from 'src/app/shared/components/confirmation-dialog/confirmation-dialog.component';
import { MatDialog } from '@angular/material/dialog';
import { API_URL } from 'src/app/shared/constants/api-url.constants';
import { takeUntil } from 'rxjs';
import { AppToasterService } from 'src/app/shared/services';
import { GroupClassesService } from 'src/app/pages/schedule-classes/pages/group-class/services';
import { GroupClassView } from 'src/app/pages/schedule-classes/pages/group-class/models/group-class.model';
import { CBGetResponse } from 'src/app/shared/models';
import { SchedulerService } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/services';
import { DashIfEmptyPipe, LocalDatePipe } from 'src/app/shared/pipe';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule],
  PIPES: [DashIfEmptyPipe, LocalDatePipe]
};

@Component({
  selector: 'app-view-group-class',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './view-group-class.component.html',
  styleUrl: './view-group-class.component.scss'
})
export class ViewGroupClassComponent extends BaseComponent implements OnInit {
  @Input() selectedGroupId!: number | null;
  @Input() selectedTabOption!: string | null;

  groupClassDetail!: GroupClassView | undefined;

  @Output() closeViewSideNav = new EventEmitter<void>();
  @Output() groupClassUpdated = new EventEmitter<void>();
  @Output() openEditSideNav = new EventEmitter<void>();

  constructor(
    private readonly dialog: MatDialog,
    private readonly toasterService: AppToasterService,
    private readonly groupClassService: GroupClassesService,
    protected readonly schedulerService: SchedulerService,
    private readonly cdr: ChangeDetectorRef
  ) {
    super();
  }

  ngOnInit(): void {
    this.getGroupClassDetail(this.selectedGroupId);
  }

  getGroupClassDetail(id: number | null): void {
    this.showPageLoader = true;
    this.groupClassService
      .get<CBGetResponse<GroupClassView>>(
        `${API_URL.groupClassScheduleSummaries.getGroupClassScheduleSummaryForView}?id=${id}`
      )
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (res: CBGetResponse<GroupClassView>) => {
          this.groupClassDetail = res.result;
          this.showPageLoader = false;
          this.cdr.detectChanges();
        },
        error: () => {
          this.showPageLoader = false;
          this.cdr.detectChanges();
        }
      });
  }

  deleteGroupConfirmation(): void {
    const dialogRef = this.dialog.open(ConfirmationDialogComponent, {
      data: {
        title: `Delete Group Class`,
        message: `Are you sure you want to delete this Group Class?`
      }
    });

    dialogRef.afterClosed().subscribe((result) => {
      if (result.isConfirmed) {
        this.deleteGroup(this.groupClassDetail!.groupClassScheduleSummary.id);
      }
    });
  }

  deleteGroup(groupId: number): void {
    this.groupClassService
      .delete(groupId, API_URL.crud.delete)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: () => {
          this.closeViewSideNavFun();
          this.groupClassUpdated.emit();
          this.toasterService.success(
            this.constants.successMessages.deletedSuccessfully.replace('{item}', 'Group Class')
          );
          this.cdr.detectChanges();
        }
      });
  }

  navigateToEdit(): void {
    this.openEditSideNav.emit();
  }

  closeViewSideNavFun(): void {
    this.closeViewSideNav.emit();
  }
}
