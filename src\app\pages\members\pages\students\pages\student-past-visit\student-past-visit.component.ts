import { CommonModule } from '@angular/common';
import {
  Component,
  EventEmitter,
  Input,
  OnChanges,
  Output,
  SimpleChanges
} from '@angular/core';
import { BaseComponent } from 'src/app/shared/components/base-component/base.component';
import { SharedModule } from 'src/app/shared/shared.module';
import { AttendanceType, DependentInformations, StudentAttendance } from '../../models';
import { MatButtonModule } from '@angular/material/button';
import { EnumToKeyValuePipe, LocalDatePipe } from 'src/app/shared/pipe';
import { NgxPaginationModule } from 'ngx-pagination';
import { ClassTypes } from 'src/app/pages/scheduler/pages/scheduler-wrapper/pages/schedule/models';
import { CommonUtils } from 'src/app/shared/utils';
import { MatTooltipModule } from '@angular/material/tooltip';

const DEPENDENCIES = {
  MODULES: [CommonModule, MatButtonModule, SharedModule, NgxPaginationModule, MatTooltipModule],
  PIPES: [EnumToKeyValuePipe, LocalDatePipe]
};

@Component({
  selector: 'app-student-past-visit',
  standalone: true,
  imports: [...DEPENDENCIES.MODULES, ...DEPENDENCIES.PIPES],
  templateUrl: './student-past-visit.component.html',
  styleUrl: './student-past-visit.component.scss'
})
export class StudentPastVisitComponent extends BaseComponent implements OnChanges {
  @Input() selectedStudentDetails!: DependentInformations | undefined;
  @Input() studentAttendances!: Array<StudentAttendance>;
  @Input() totalCount!: number;
  @Input() selectedTabOption!: number;

  attendanceTypes = AttendanceType;
  classTypes = ClassTypes;
  currentPage = this.paginationConfig.pageNumber;
  pageSize = this.paginationConfig.itemsPerPage;

  @Output() closeSideNav = new EventEmitter<void>();
  @Output() getStudentAttendance = new EventEmitter<{page: number, pageSize: number, status: number}>();

  constructor() {
    super();
  }

  ngOnChanges(changes: SimpleChanges) {
    this.showPageLoader = true;
    if (changes['studentAttendances']?.currentValue) {
      this.studentAttendances = changes['studentAttendances']?.currentValue;
      this.showPageLoader = false;
    }
  }

  setActiveTabOption(tabValue: number): void {
    this.selectedTabOption = tabValue;
    this.getStudentAttendance.emit({ page: 1, pageSize: this.pageSize, status: this.selectedTabOption });
    this.showPageLoader = true;
  }

  onPageChange(page: number) {
    this.currentPage = page;
    this.getStudentAttendance.emit({ page: this.currentPage, pageSize: this.pageSize, status: this.selectedTabOption });
    this.showPageLoader = true;
  }

  getTimeDiff(start: string, end: string): number {
    return CommonUtils.calculateTimeDifference(new Date(start), new Date(end));
  }

  closeViewAll(): void {
    this.closeSideNav.emit();
  }
}
